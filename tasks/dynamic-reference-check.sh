#!/bin/bash

# 动态引用检查脚本
# 专门检查模板字符串和动态路径引用

PROJECT_DIR="/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app"
OUTPUT_DIR="/Users/<USER>/Dev/SideWork/heartful-mall/docs/tasks"

echo "🔍 检查动态引用模式..."

# 1. 检查模板字符串引用
echo "📝 扫描模板字符串引用..."
grep -r '\${.*}' "$PROJECT_DIR" --include="*.vue" --include="*.js" --include="*.ts" | \
    grep "static/" | \
    grep -v "node_modules" | \
    grep -v "unpackage" | \
    grep -v "docs" > "$OUTPUT_DIR/template_string_references.txt"

echo "发现的模板字符串引用:"
cat "$OUTPUT_DIR/template_string_references.txt"

# 2. 检查动态路径拼接
echo ""
echo "🔗 扫描动态路径拼接..."
grep -r "static.*\+" "$PROJECT_DIR" --include="*.vue" --include="*.js" --include="*.ts" | \
    grep -v "node_modules" | \
    grep -v "unpackage" | \
    grep -v "docs" > "$OUTPUT_DIR/dynamic_path_references.txt"

echo "发现的动态路径拼接:"
cat "$OUTPUT_DIR/dynamic_path_references.txt"

# 3. 检查条件引用
echo ""
echo "❓ 扫描条件引用..."
grep -r "v-if.*static\|v-else.*static\|v-show.*static" "$PROJECT_DIR" --include="*.vue" | \
    grep -v "node_modules" | \
    grep -v "unpackage" | \
    grep -v "docs" > "$OUTPUT_DIR/conditional_references.txt"

echo "发现的条件引用:"
cat "$OUTPUT_DIR/conditional_references.txt"

# 4. 基于发现的动态引用，更新未使用文件列表
echo ""
echo "🔄 更新未使用文件分析..."

# 检查star_系列文件
if grep -q "star_\${" "$OUTPUT_DIR/template_string_references.txt"; then
    echo "⚠️  发现star_系列文件的动态引用，这些文件应该被标记为已使用:"
    echo "  - static/user/star_1.png"
    echo "  - static/user/star_2.png" 
    echo "  - static/user/star_3.png"
    echo "  - static/user/star_4.png"
    
    # 从未使用文件列表中移除这些文件
    if [ -f "$OUTPUT_DIR/unused_files_classified.txt" ]; then
        grep -v "star_[1-4].png" "$OUTPUT_DIR/unused_files_classified.txt" > "$OUTPUT_DIR/unused_files_classified_updated.txt"
        mv "$OUTPUT_DIR/unused_files_classified_updated.txt" "$OUTPUT_DIR/unused_files_classified.txt"
    fi
fi

# 5. 重新计算统计数据
if [ -f "$OUTPUT_DIR/unused_files_classified.txt" ]; then
    UPDATED_UNUSED_COUNT=$(wc -l < "$OUTPUT_DIR/unused_files_classified.txt")
    UPDATED_UNUSED_SIZE=$(awk '{sum += $2} END {print sum}' "$OUTPUT_DIR/unused_files_classified.txt")
    UPDATED_UNUSED_SIZE_KB=$((UPDATED_UNUSED_SIZE / 1024))
    
    echo ""
    echo "📊 更新后的未使用资源统计:"
    echo "  总数: $UPDATED_UNUSED_COUNT 个文件"
    echo "  总大小: $UPDATED_UNUSED_SIZE 字节 ($UPDATED_UNUSED_SIZE_KB KB)"
    
    # 按风险等级重新统计
    LOW_COUNT=$(grep -c "LOW_RISK" "$OUTPUT_DIR/unused_files_classified.txt" || echo "0")
    MEDIUM_COUNT=$(grep -c "MEDIUM_RISK" "$OUTPUT_DIR/unused_files_classified.txt" || echo "0")
    HIGH_COUNT=$(grep -c "HIGH_RISK" "$OUTPUT_DIR/unused_files_classified.txt" || echo "0")
    
    echo "  低风险: $LOW_COUNT 个"
    echo "  中风险: $MEDIUM_COUNT 个"
    echo "  高风险: $HIGH_COUNT 个"
fi

echo ""
echo "✅ 动态引用检查完成！"
