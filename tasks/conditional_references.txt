/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/packageTest/components/productDetail/productDetail.vue:					<image v-if="showGoCart" class="productDetail-info-other-ys-goCart" src="@/static/index/btn1.png"
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/packageGoods/pages/productInfo/productInfo_20250610230531.vue:					<image v-if="infos.isCollect == 1" src="@/static/productInfo/collect.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/packageGoods/pages/productInfo/productInfo_20250610230531.vue:					<image v-else src="@/static/productInfo/noCollect.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/packageGoods/pages/productInfo/productInfo_20250528142437.vue:					<image v-if="infos.isCollect == 1" src="@/static/productInfo/collect.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/packageGoods/pages/productInfo/productInfo_20250528142437.vue:					<image v-else src="@/static/productInfo/noCollect.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604232013.vue:				<image v-if='payType == "0"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604232013.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604232013.vue:				<image v-if='payType == "1"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604232013.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604231938.vue:				<image v-if='payType == "0"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604231938.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604231938.vue:				<image v-if='payType == "1"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250604231938.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250514180625.vue:				<image v-if='payType == "0"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250514180625.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250514180625.vue:				<image v-if='payType == "1"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/.history/pages/cashier/cashier_20250514180625.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/packageGoods/pages/productInfo/productInfo.vue:					<image v-if="infos.isCollect == 1" src="@/static/productInfo/collect.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/packageGoods/pages/productInfo/productInfo.vue:					<image v-else src="@/static/productInfo/noCollect.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/pages/cashier/cashier.vue:				<image v-if='payType == "0"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/pages/cashier/cashier.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/pages/cashier/cashier.vue:				<image v-if='payType == "1"' src="@/static/img/cashier/sel.png" mode=""></image>
/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app/pages/cashier/cashier.vue:				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
