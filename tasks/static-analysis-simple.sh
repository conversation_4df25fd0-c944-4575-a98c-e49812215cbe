#!/bin/bash

# 微信小程序静态资源分析脚本
# 目标：分析 heartful-mall-app 项目的静态资源使用情况

PROJECT_DIR="/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app"
OUTPUT_DIR="/Users/<USER>/Dev/SideWork/heartful-mall/docs/tasks"

echo "🚀 开始静态资源分析..."
echo "项目目录: $PROJECT_DIR"
echo "输出目录: $OUTPUT_DIR"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 1. 扫描所有静态资源文件
echo "📁 扫描静态资源文件..."
find "$PROJECT_DIR/static" -type f \( -name "*.png" -o -name "*.jpg" -o -name "*.jpeg" -o -name "*.gif" -o -name "*.svg" -o -name "*.webp" -o -name "*.mp3" -o -name "*.mp4" -o -name "*.wav" -o -name "*.ttf" -o -name "*.woff" -o -name "*.woff2" -o -name "*.ico" \) > "$OUTPUT_DIR/all_static_files.txt"

TOTAL_FILES=$(wc -l < "$OUTPUT_DIR/all_static_files.txt")
echo "📊 发现 $TOTAL_FILES 个静态资源文件"

# 2. 计算文件大小并排序
echo "📏 计算文件大小..."
while read -r file; do
    size=$(stat -f%z "$file" 2>/dev/null || echo "0")
    relative_path=$(echo "$file" | sed "s|$PROJECT_DIR/||")
    echo "$size $relative_path"
done < "$OUTPUT_DIR/all_static_files.txt" | sort -nr > "$OUTPUT_DIR/files_by_size.txt"

# 3. 计算总大小
TOTAL_SIZE=$(awk '{sum += $1} END {print sum}' "$OUTPUT_DIR/files_by_size.txt")
TOTAL_SIZE_KB=$((TOTAL_SIZE / 1024))
echo "💾 静态资源总大小: $TOTAL_SIZE 字节 ($TOTAL_SIZE_KB KB)"

# 4. 找出最大的10个文件
echo "🔍 最大的10个文件:"
head -10 "$OUTPUT_DIR/files_by_size.txt" | while read -r size path; do
    size_kb=$((size / 1024))
    echo "  $path: ${size_kb}KB"
done

# 5. 扫描代码中的引用
echo "🔗 扫描代码引用..."
grep -r "static/" "$PROJECT_DIR" --include="*.vue" --include="*.js" --include="*.ts" --include="*.json" | \
    grep -v "node_modules" | \
    grep -v "unpackage" | \
    grep -v "docs" | \
    sed 's/.*static\//static\//' | \
    sed 's/["'"'"'].*$//' | \
    sed 's/[)>].*$//' | \
    sort | uniq > "$OUTPUT_DIR/referenced_files.txt"

REFERENCED_COUNT=$(wc -l < "$OUTPUT_DIR/referenced_files.txt")
echo "📋 发现 $REFERENCED_COUNT 个被引用的资源路径"

# 6. 找出未被引用的文件
echo "🎯 识别未使用的资源..."
while read -r size path; do
    # 标准化路径
    normalized_path=$(echo "$path" | sed 's|^static/||')
    
    # 检查是否被引用
    if ! grep -q "$normalized_path" "$OUTPUT_DIR/referenced_files.txt"; then
        # 检查是否为关键文件（白名单）
        if [[ "$path" == *"tabbar"* ]] || [[ "$path" == *"logo"* ]] || [[ "$path" == *"missing-face"* ]]; then
            echo "HIGH_RISK $size $path" >> "$OUTPUT_DIR/unused_files_classified.txt"
        elif [[ "$path" == *"icon"* ]] || [[ "$path" == *"btn"* ]] || [[ $size -gt 102400 ]]; then
            echo "MEDIUM_RISK $size $path" >> "$OUTPUT_DIR/unused_files_classified.txt"
        else
            echo "LOW_RISK $size $path" >> "$OUTPUT_DIR/unused_files_classified.txt"
        fi
    fi
done < "$OUTPUT_DIR/files_by_size.txt"

# 7. 统计未使用文件
if [ -f "$OUTPUT_DIR/unused_files_classified.txt" ]; then
    UNUSED_COUNT=$(wc -l < "$OUTPUT_DIR/unused_files_classified.txt")
    UNUSED_SIZE=$(awk '{sum += $2} END {print sum}' "$OUTPUT_DIR/unused_files_classified.txt")
    UNUSED_SIZE_KB=$((UNUSED_SIZE / 1024))
    
    echo "🗑️  未使用资源统计:"
    echo "  总数: $UNUSED_COUNT 个文件"
    echo "  总大小: $UNUSED_SIZE 字节 ($UNUSED_SIZE_KB KB)"
    
    # 按风险等级统计
    LOW_COUNT=$(grep -c "LOW_RISK" "$OUTPUT_DIR/unused_files_classified.txt" || echo "0")
    MEDIUM_COUNT=$(grep -c "MEDIUM_RISK" "$OUTPUT_DIR/unused_files_classified.txt" || echo "0")
    HIGH_COUNT=$(grep -c "HIGH_RISK" "$OUTPUT_DIR/unused_files_classified.txt" || echo "0")
    
    echo "  低风险: $LOW_COUNT 个"
    echo "  中风险: $MEDIUM_COUNT 个"
    echo "  高风险: $HIGH_COUNT 个"
    
    # 计算可节省的空间
    SAVINGS_PERCENTAGE=$(echo "scale=2; $UNUSED_SIZE * 100 / $TOTAL_SIZE" | bc -l 2>/dev/null || echo "0")
    echo "  可节省空间比例: ${SAVINGS_PERCENTAGE}%"
else
    echo "✅ 所有静态资源都被使用"
fi

# 8. 生成详细报告
echo "📝 生成分析报告..."
cat > "$OUTPUT_DIR/static-analysis-report.md" << EOF
# 微信小程序静态资源优化分析报告

## 执行摘要

**分析时间**: $(date)  
**项目名称**: 八闽助业集市微信小程序  
**分析目标**: 减少主包体积至2MB以下  

### 关键发现
- 总静态资源数量: $TOTAL_FILES 个
- 静态资源总大小: $TOTAL_SIZE_KB KB
- 未使用资源数量: ${UNUSED_COUNT:-0} 个
- 可节省空间: ${UNUSED_SIZE_KB:-0} KB (${SAVINGS_PERCENTAGE:-0}%)

## 详细分析结果

### 1. 静态资源总览

#### 文件大小分布
\`\`\`
总文件数: $TOTAL_FILES
总大小: $TOTAL_SIZE_KB KB
平均大小: $((TOTAL_SIZE_KB / TOTAL_FILES)) KB
\`\`\`

#### 最大的10个文件
EOF

# 添加最大文件列表到报告
head -10 "$OUTPUT_DIR/files_by_size.txt" | while read -r size path; do
    size_kb=$((size / 1024))
    echo "- $path: ${size_kb}KB" >> "$OUTPUT_DIR/static-analysis-report.md"
done

cat >> "$OUTPUT_DIR/static-analysis-report.md" << EOF

### 2. 未使用资源分析

#### 按风险等级分类
- **低风险** (可安全删除): ${LOW_COUNT:-0} 个
- **中风险** (需要验证): ${MEDIUM_COUNT:-0} 个  
- **高风险** (建议保留): ${HIGH_COUNT:-0} 个

#### 未使用资源详细列表
EOF

# 添加未使用文件列表
if [ -f "$OUTPUT_DIR/unused_files_classified.txt" ]; then
    echo "" >> "$OUTPUT_DIR/static-analysis-report.md"
    echo "##### 低风险文件 (可安全删除)" >> "$OUTPUT_DIR/static-analysis-report.md"
    grep "LOW_RISK" "$OUTPUT_DIR/unused_files_classified.txt" | while read -r risk size path; do
        size_kb=$((size / 1024))
        echo "- $path (${size_kb}KB)" >> "$OUTPUT_DIR/static-analysis-report.md"
    done
    
    echo "" >> "$OUTPUT_DIR/static-analysis-report.md"
    echo "##### 中风险文件 (需要验证)" >> "$OUTPUT_DIR/static-analysis-report.md"
    grep "MEDIUM_RISK" "$OUTPUT_DIR/unused_files_classified.txt" | while read -r risk size path; do
        size_kb=$((size / 1024))
        echo "- $path (${size_kb}KB)" >> "$OUTPUT_DIR/static-analysis-report.md"
    done
    
    echo "" >> "$OUTPUT_DIR/static-analysis-report.md"
    echo "##### 高风险文件 (建议保留)" >> "$OUTPUT_DIR/static-analysis-report.md"
    grep "HIGH_RISK" "$OUTPUT_DIR/unused_files_classified.txt" | while read -r risk size path; do
        size_kb=$((size / 1024))
        echo "- $path (${size_kb}KB)" >> "$OUTPUT_DIR/static-analysis-report.md"
    done
fi

cat >> "$OUTPUT_DIR/static-analysis-report.md" << EOF

### 3. 优化建议

#### 立即可执行的优化
1. 删除确认未使用的低风险资源 (可节省 $(grep "LOW_RISK" "$OUTPUT_DIR/unused_files_classified.txt" 2>/dev/null | awk '{sum += $2} END {print int(sum/1024)}' || echo "0") KB)
2. 压缩大尺寸图片文件 (重点关注 >50KB 的图片)
3. 考虑将部分图片转换为WebP格式

#### 需要进一步验证的优化
1. 中风险资源的业务逻辑确认
2. 动态引用路径的深度分析
3. 条件渲染中使用的资源验证

### 4. 清理脚本

#### 安全删除低风险文件
\`\`\`bash
# 备份当前static目录
cp -r static static_backup_\$(date +%Y%m%d_%H%M%S)

# 删除低风险未使用文件
EOF

if [ -f "$OUTPUT_DIR/unused_files_classified.txt" ]; then
    grep "LOW_RISK" "$OUTPUT_DIR/unused_files_classified.txt" | while read -r risk size path; do
        echo "rm \"$PROJECT_DIR/$path\"" >> "$OUTPUT_DIR/static-analysis-report.md"
    done
fi

cat >> "$OUTPUT_DIR/static-analysis-report.md" << EOF
\`\`\`

#### 回滚脚本
\`\`\`bash
# 如果出现问题，可以恢复备份
# rm -rf static
# mv static_backup_YYYYMMDD_HHMMSS static
\`\`\`

---
*本报告由静态资源分析工具自动生成于 $(date)*
EOF

echo "✅ 分析完成！"
echo "📄 详细报告已保存到: $OUTPUT_DIR/static-analysis-report.md"
echo "📊 数据文件保存在: $OUTPUT_DIR/"
echo ""
echo "🎯 关键结果:"
echo "  - 总资源: $TOTAL_FILES 个文件 ($TOTAL_SIZE_KB KB)"
echo "  - 未使用: ${UNUSED_COUNT:-0} 个文件 (${UNUSED_SIZE_KB:-0} KB)"
echo "  - 可节省: ${SAVINGS_PERCENTAGE:-0}% 的静态资源空间"
