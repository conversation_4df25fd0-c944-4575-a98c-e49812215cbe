#!/bin/bash

# 微信小程序图片优化脚本
# 压缩大尺寸图片以减少包体积

PROJECT_DIR="/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app"
BACKUP_DIR="$PROJECT_DIR/static_images_backup_$(date +%Y%m%d_%H%M%S)"

echo "🖼️ 开始图片优化..."
echo "项目目录: $PROJECT_DIR"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ 错误: 项目目录不存在: $PROJECT_DIR"
    exit 1
fi

# 检查是否安装了imagemagick
if ! command -v convert &> /dev/null; then
    echo "⚠️  ImageMagick未安装，将使用系统自带的sips工具"
    USE_SIPS=true
else
    echo "✅ 检测到ImageMagick，将使用convert命令"
    USE_SIPS=false
fi

# 1. 创建备份
echo "📦 创建图片备份..."
mkdir -p "$BACKUP_DIR"

# 2. 定义需要优化的大图片文件（>50KB）
declare -a LARGE_IMAGES=(
    "static/index/top_bg.png"
    "static/productionAbout/s_2.png"
    "static/helpPanel/example.png"
    "static/login/bg.png"
    "static/payAboutModal/star.png"
    "static/productionAbout/s_0.png"
    "static/index/btn.png"
    "static/confirmOrder/tj_bg.png"
)

# 3. 分析当前图片大小
echo "📊 分析当前图片大小..."
TOTAL_SIZE_BEFORE=0
for image in "${LARGE_IMAGES[@]}"; do
    if [ -f "$PROJECT_DIR/$image" ]; then
        size=$(stat -f%z "$PROJECT_DIR/$image" 2>/dev/null || echo "0")
        size_kb=$((size / 1024))
        TOTAL_SIZE_BEFORE=$((TOTAL_SIZE_BEFORE + size))
        echo "  $image: ${size_kb}KB"
        
        # 备份原图
        mkdir -p "$BACKUP_DIR/$(dirname "$image")"
        cp "$PROJECT_DIR/$image" "$BACKUP_DIR/$image"
    fi
done

TOTAL_SIZE_BEFORE_KB=$((TOTAL_SIZE_BEFORE / 1024))
echo "大图片总大小: $TOTAL_SIZE_BEFORE_KB KB"

# 4. 优化函数
optimize_image() {
    local input_file="$1"
    local output_file="$2"
    local quality="$3"
    
    if [ "$USE_SIPS" = true ]; then
        # 使用macOS自带的sips工具
        sips -s format jpeg -s formatOptions "$quality" "$input_file" --out "$output_file" 2>/dev/null
        if [ $? -ne 0 ]; then
            # 如果转换失败，尝试PNG压缩
            sips -s format png "$input_file" --out "$output_file" 2>/dev/null
        fi
    else
        # 使用ImageMagick
        convert "$input_file" -quality "$quality" -strip "$output_file"
    fi
}

# 5. 执行优化
echo ""
echo "🔧 开始优化图片..."

for image in "${LARGE_IMAGES[@]}"; do
    if [ -f "$PROJECT_DIR/$image" ]; then
        echo "处理: $image"
        
        # 获取原始大小
        original_size=$(stat -f%z "$PROJECT_DIR/$image")
        original_size_kb=$((original_size / 1024))
        
        # 创建临时文件
        temp_file="$PROJECT_DIR/${image}.tmp"
        
        # 根据文件类型选择优化策略
        if [[ "$image" == *.png ]]; then
            # PNG文件：尝试转换为JPEG（如果是照片类图片）或压缩PNG
            if [[ "$image" == *"bg.png" ]] || [[ "$image" == *"star.png" ]]; then
                # 背景图和照片类图片转换为JPEG
                jpeg_file="${image%.*}.jpg"
                optimize_image "$PROJECT_DIR/$image" "$PROJECT_DIR/$jpeg_file" 85
                
                if [ -f "$PROJECT_DIR/$jpeg_file" ]; then
                    new_size=$(stat -f%z "$PROJECT_DIR/$jpeg_file")
                    new_size_kb=$((new_size / 1024))
                    
                    if [ $new_size -lt $original_size ]; then
                        echo "  ✅ 转换为JPEG: ${original_size_kb}KB → ${new_size_kb}KB (节省 $((original_size_kb - new_size_kb))KB)"
                        echo "  ⚠️  注意: 需要更新代码中的文件引用从 .png 改为 .jpg"
                    else
                        echo "  ❌ JPEG转换未能减小文件大小，保持原格式"
                        rm "$PROJECT_DIR/$jpeg_file"
                    fi
                fi
            else
                # 图标类PNG文件保持PNG格式但压缩
                optimize_image "$PROJECT_DIR/$image" "$temp_file" 90
                
                if [ -f "$temp_file" ]; then
                    new_size=$(stat -f%z "$temp_file")
                    new_size_kb=$((new_size / 1024))
                    
                    if [ $new_size -lt $original_size ]; then
                        mv "$temp_file" "$PROJECT_DIR/$image"
                        echo "  ✅ PNG压缩: ${original_size_kb}KB → ${new_size_kb}KB (节省 $((original_size_kb - new_size_kb))KB)"
                    else
                        echo "  ❌ 压缩未能减小文件大小，保持原文件"
                        rm "$temp_file"
                    fi
                fi
            fi
        else
            # 其他格式文件
            optimize_image "$PROJECT_DIR/$image" "$temp_file" 85
            
            if [ -f "$temp_file" ]; then
                new_size=$(stat -f%z "$temp_file")
                new_size_kb=$((new_size / 1024))
                
                if [ $new_size -lt $original_size ]; then
                    mv "$temp_file" "$PROJECT_DIR/$image"
                    echo "  ✅ 压缩: ${original_size_kb}KB → ${new_size_kb}KB (节省 $((original_size_kb - new_size_kb))KB)"
                else
                    echo "  ❌ 压缩未能减小文件大小，保持原文件"
                    rm "$temp_file"
                fi
            fi
        fi
    else
        echo "⚠️  文件不存在: $image"
    fi
done

# 6. 计算优化结果
echo ""
echo "📊 计算优化结果..."
TOTAL_SIZE_AFTER=0
for image in "${LARGE_IMAGES[@]}"; do
    if [ -f "$PROJECT_DIR/$image" ]; then
        size=$(stat -f%z "$PROJECT_DIR/$image")
        TOTAL_SIZE_AFTER=$((TOTAL_SIZE_AFTER + size))
    fi
    
    # 检查是否有对应的JPEG文件
    jpeg_file="${image%.*}.jpg"
    if [ -f "$PROJECT_DIR/$jpeg_file" ]; then
        size=$(stat -f%z "$PROJECT_DIR/$jpeg_file")
        TOTAL_SIZE_AFTER=$((TOTAL_SIZE_AFTER + size))
    fi
done

TOTAL_SIZE_AFTER_KB=$((TOTAL_SIZE_AFTER / 1024))
SAVED_SIZE=$((TOTAL_SIZE_BEFORE - TOTAL_SIZE_AFTER))
SAVED_SIZE_KB=$((SAVED_SIZE / 1024))
SAVED_PERCENTAGE=$(echo "scale=1; $SAVED_SIZE * 100 / $TOTAL_SIZE_BEFORE" | bc -l 2>/dev/null || echo "0")

echo "优化前总大小: $TOTAL_SIZE_BEFORE_KB KB"
echo "优化后总大小: $TOTAL_SIZE_AFTER_KB KB"
echo "节省空间: $SAVED_SIZE_KB KB (${SAVED_PERCENTAGE}%)"

# 7. 生成回滚脚本
ROLLBACK_SCRIPT="$PROJECT_DIR/rollback-image-optimization.sh"
cat > "$ROLLBACK_SCRIPT" << EOF
#!/bin/bash
# 图片优化回滚脚本
# 自动生成于 $(date)

echo "🔄 开始回滚图片优化..."

if [ -d "$BACKUP_DIR" ]; then
    echo "📦 恢复图片备份..."
    
    # 恢复所有备份的图片
    cp -r "$BACKUP_DIR"/* "$PROJECT_DIR/"
    
    # 删除可能生成的JPEG文件
    find "$PROJECT_DIR/static" -name "*.jpg" -delete
    
    echo "✅ 图片回滚完成"
    
    # 删除备份目录和此脚本
    rm -rf "$BACKUP_DIR"
    rm "\$0"
else
    echo "❌ 备份目录不存在: $BACKUP_DIR"
    exit 1
fi
EOF

chmod +x "$ROLLBACK_SCRIPT"

# 8. 显示结果和建议
echo ""
echo "✅ 图片优化完成！"
echo "💾 节省了 $SAVED_SIZE_KB KB 的空间"
echo "📦 备份位置: $BACKUP_DIR"
echo "📝 回滚脚本: $ROLLBACK_SCRIPT"

echo ""
echo "🎯 重要提醒:"
echo "1. 如果有PNG转换为JPEG的文件，需要更新代码中的文件引用"
echo "2. 重新编译项目并测试图片显示效果"
echo "3. 检查图片质量是否满足要求"
echo "4. 如果有问题，运行回滚脚本恢复原图片"

# 9. 检查是否有需要更新引用的文件
echo ""
echo "🔍 检查需要更新引用的文件:"
for image in "${LARGE_IMAGES[@]}"; do
    jpeg_file="${image%.*}.jpg"
    if [ -f "$PROJECT_DIR/$jpeg_file" ] && [ -f "$PROJECT_DIR/$image" ]; then
        echo "⚠️  发现同时存在 $image 和 $jpeg_file"
        echo "   建议删除原PNG文件并更新代码引用"
    fi
done
