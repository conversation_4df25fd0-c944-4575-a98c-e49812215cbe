#!/bin/bash

# 微信小程序静态资源清理脚本
# 安全删除确认未使用的低风险静态资源

PROJECT_DIR="/Users/<USER>/Dev/SideWork/heartful-mall/heartful-mall-app"
BACKUP_DIR="$PROJECT_DIR/static_backup_$(date +%Y%m%d_%H%M%S)"

echo "🚀 开始清理未使用的静态资源..."
echo "项目目录: $PROJECT_DIR"

# 检查项目目录是否存在
if [ ! -d "$PROJECT_DIR" ]; then
    echo "❌ 错误: 项目目录不存在: $PROJECT_DIR"
    exit 1
fi

# 检查static目录是否存在
if [ ! -d "$PROJECT_DIR/static" ]; then
    echo "❌ 错误: static目录不存在: $PROJECT_DIR/static"
    exit 1
fi

# 1. 创建备份
echo "📦 创建备份..."
cp -r "$PROJECT_DIR/static" "$BACKUP_DIR"
if [ $? -eq 0 ]; then
    echo "✅ 备份创建成功: $BACKUP_DIR"
else
    echo "❌ 备份创建失败"
    exit 1
fi

# 2. 定义要删除的低风险文件列表
declare -a FILES_TO_DELETE=(
    "static/heartPool/sm_bg.png"
    "static/cart/cart_bak.png"
    "static/payAboutModal/desc.png"
    "static/productInfo/custom.png"
    "static/cart/top.png"
    "static/applyForAfterSale/edit.png"
    "static/heartPool/search.png"
    "static/img/cashier/bankCard.png"
    "static/cart/noselect.png"
    "static/bottom-arrow-gray.png"
)

# 3. 计算删除前的总大小
echo "📊 计算删除前的文件大小..."
TOTAL_SIZE_BEFORE=0
for file in "${FILES_TO_DELETE[@]}"; do
    if [ -f "$PROJECT_DIR/$file" ]; then
        size=$(stat -f%z "$PROJECT_DIR/$file" 2>/dev/null || echo "0")
        TOTAL_SIZE_BEFORE=$((TOTAL_SIZE_BEFORE + size))
    fi
done
TOTAL_SIZE_BEFORE_KB=$((TOTAL_SIZE_BEFORE / 1024))
echo "将要删除的文件总大小: $TOTAL_SIZE_BEFORE 字节 ($TOTAL_SIZE_BEFORE_KB KB)"

# 4. 确认删除操作
echo ""
echo "⚠️  即将删除以下文件:"
for file in "${FILES_TO_DELETE[@]}"; do
    if [ -f "$PROJECT_DIR/$file" ]; then
        size=$(stat -f%z "$PROJECT_DIR/$file" 2>/dev/null || echo "0")
        size_kb=$((size / 1024))
        echo "  - $file (${size_kb}KB)"
    else
        echo "  - $file (文件不存在)"
    fi
done

echo ""
read -p "确认删除这些文件吗? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ 操作已取消"
    # 删除刚创建的备份
    rm -rf "$BACKUP_DIR"
    exit 0
fi

# 5. 执行删除操作
echo "🗑️ 开始删除文件..."
DELETED_COUNT=0
DELETED_SIZE=0

for file in "${FILES_TO_DELETE[@]}"; do
    if [ -f "$PROJECT_DIR/$file" ]; then
        size=$(stat -f%z "$PROJECT_DIR/$file" 2>/dev/null || echo "0")
        rm "$PROJECT_DIR/$file"
        if [ $? -eq 0 ]; then
            echo "✅ 已删除: $file"
            DELETED_COUNT=$((DELETED_COUNT + 1))
            DELETED_SIZE=$((DELETED_SIZE + size))
        else
            echo "❌ 删除失败: $file"
        fi
    else
        echo "⚠️  文件不存在: $file"
    fi
done

# 6. 清理空目录
echo ""
echo "🧹 清理空目录..."
find "$PROJECT_DIR/static" -type d -empty -delete 2>/dev/null
echo "✅ 空目录清理完成"

# 7. 显示结果
DELETED_SIZE_KB=$((DELETED_SIZE / 1024))
echo ""
echo "📊 清理结果:"
echo "  删除文件数: $DELETED_COUNT 个"
echo "  节省空间: $DELETED_SIZE 字节 ($DELETED_SIZE_KB KB)"
echo "  备份位置: $BACKUP_DIR"

# 8. 生成回滚脚本
ROLLBACK_SCRIPT="$PROJECT_DIR/rollback-static-cleanup.sh"
cat > "$ROLLBACK_SCRIPT" << EOF
#!/bin/bash
# 静态资源清理回滚脚本
# 自动生成于 $(date)

echo "🔄 开始回滚静态资源清理..."

if [ -d "$BACKUP_DIR" ]; then
    echo "📦 恢复备份: $BACKUP_DIR"
    rm -rf "$PROJECT_DIR/static"
    mv "$BACKUP_DIR" "$PROJECT_DIR/static"
    echo "✅ 回滚完成"
    
    # 删除此回滚脚本
    rm "\$0"
else
    echo "❌ 备份目录不存在: $BACKUP_DIR"
    exit 1
fi
EOF

chmod +x "$ROLLBACK_SCRIPT"
echo "📝 回滚脚本已生成: $ROLLBACK_SCRIPT"

# 9. 建议下一步操作
echo ""
echo "🎯 建议下一步操作:"
echo "1. 重新编译小程序项目"
echo "2. 在开发工具中测试所有功能"
echo "3. 检查是否有图片显示异常"
echo "4. 如果有问题，运行回滚脚本: $ROLLBACK_SCRIPT"
echo "5. 如果一切正常，可以删除备份目录: $BACKUP_DIR"

echo ""
echo "✅ 静态资源清理完成！"
echo "💾 节省了 $DELETED_SIZE_KB KB 的空间"
