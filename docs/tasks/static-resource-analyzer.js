#!/usr/bin/env node

/**
 * 微信小程序静态资源分析工具
 * 功能：扫描项目中的静态资源，分析使用情况，识别未使用的资源
 */

const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

class StaticResourceAnalyzer {
    constructor(projectRoot) {
        this.projectRoot = projectRoot;
        this.staticResources = new Map(); // 存储所有静态资源
        this.references = new Map(); // 存储引用关系
        this.config = {
            // 支持的静态资源文件扩展名
            staticExtensions: ['.png', '.jpg', '.jpeg', '.gif', '.svg', '.webp', 
                             '.mp3', '.mp4', '.wav', '.ttf', '.woff', '.woff2', '.ico'],
            // 需要扫描的代码文件扩展名
            codeExtensions: ['.vue', '.js', '.ts', '.css', '.scss', '.less', '.json'],
            // 静态资源目录
            staticDirs: ['static', 'assets', 'images'],
            // 排除的目录
            excludeDirs: ['node_modules', '.git', 'unpackage', 'dist'],
            // 白名单文件（系统关键文件，不应删除）
            whitelist: [
                'static/logo.png',
                'static/tabbar/',
                'static/missing-face.png'
            ]
        };
    }

    /**
     * 开始分析
     */
    async analyze() {
        console.log('🚀 开始静态资源分析...');
        
        try {
            // 步骤1：扫描所有静态资源
            await this.scanStaticResources();
            
            // 步骤2：扫描代码文件中的引用
            await this.scanCodeReferences();
            
            // 步骤3：分析未使用的资源
            const unusedResources = this.findUnusedResources();
            
            // 步骤4：生成报告
            const report = this.generateReport(unusedResources);
            
            // 步骤5：保存结果
            await this.saveResults(report, unusedResources);
            
            console.log('✅ 分析完成！');
            return report;
            
        } catch (error) {
            console.error('❌ 分析过程中出现错误:', error);
            throw error;
        }
    }

    /**
     * 递归扫描静态资源文件
     */
    async scanStaticResources() {
        console.log('📁 扫描静态资源文件...');
        
        const scanDir = (dirPath, relativePath = '') => {
            if (!fs.existsSync(dirPath)) return;
            
            const items = fs.readdirSync(dirPath);
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const relativeItemPath = path.join(relativePath, item);
                
                // 跳过排除的目录
                if (this.config.excludeDirs.includes(item)) continue;
                
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    scanDir(fullPath, relativeItemPath);
                } else if (stat.isFile()) {
                    const ext = path.extname(item).toLowerCase();
                    
                    // 检查是否为静态资源文件
                    if (this.config.staticExtensions.includes(ext)) {
                        const resourceInfo = {
                            path: relativeItemPath.replace(/\\/g, '/'), // 标准化路径分隔符
                            fullPath: fullPath,
                            size: stat.size,
                            type: ext.substring(1), // 去掉点号
                            isUsed: false,
                            usedBy: [],
                            riskLevel: 'low' // 默认低风险
                        };
                        
                        this.staticResources.set(resourceInfo.path, resourceInfo);
                    }
                }
            }
        };
        
        // 扫描项目根目录
        scanDir(this.projectRoot);
        
        console.log(`📊 发现 ${this.staticResources.size} 个静态资源文件`);
    }

    /**
     * 扫描代码文件中的资源引用
     */
    async scanCodeReferences() {
        console.log('🔍 扫描代码引用关系...');
        
        const scanDir = (dirPath, relativePath = '') => {
            if (!fs.existsSync(dirPath)) return;
            
            const items = fs.readdirSync(dirPath);
            
            for (const item of items) {
                const fullPath = path.join(dirPath, item);
                const relativeItemPath = path.join(relativePath, item);
                
                // 跳过排除的目录
                if (this.config.excludeDirs.includes(item)) continue;
                
                const stat = fs.statSync(fullPath);
                
                if (stat.isDirectory()) {
                    scanDir(fullPath, relativeItemPath);
                } else if (stat.isFile()) {
                    const ext = path.extname(item).toLowerCase();
                    
                    // 检查是否为代码文件
                    if (this.config.codeExtensions.includes(ext)) {
                        this.scanFileReferences(fullPath, relativeItemPath);
                    }
                }
            }
        };
        
        scanDir(this.projectRoot);
        
        console.log(`🔗 扫描完成，发现 ${this.references.size} 个引用关系`);
    }

    /**
     * 扫描单个文件中的资源引用
     */
    scanFileReferences(filePath, relativeFilePath) {
        try {
            const content = fs.readFileSync(filePath, 'utf-8');
            const references = this.extractReferences(content, relativeFilePath);
            
            for (const ref of references) {
                const normalizedPath = this.normalizePath(ref.path);
                
                // 检查引用的资源是否存在
                if (this.staticResources.has(normalizedPath)) {
                    const resource = this.staticResources.get(normalizedPath);
                    resource.isUsed = true;
                    resource.usedBy.push({
                        file: relativeFilePath,
                        line: ref.line,
                        type: ref.type
                    });
                    
                    // 存储引用关系
                    const refKey = `${relativeFilePath}:${ref.line}`;
                    this.references.set(refKey, {
                        sourceFile: relativeFilePath,
                        targetResource: normalizedPath,
                        line: ref.line,
                        type: ref.type
                    });
                }
            }
        } catch (error) {
            console.warn(`⚠️  读取文件失败: ${filePath}`, error.message);
        }
    }

    /**
     * 从文件内容中提取资源引用
     */
    extractReferences(content, filePath) {
        const references = [];
        const lines = content.split('\n');
        
        // 定义各种引用模式的正则表达式
        const patterns = [
            // Vue template 中的 src 属性
            { regex: /src\s*=\s*["']([^"']+)["']/g, type: 'template-src' },
            // CSS 中的 url() 函数
            { regex: /url\s*\(\s*["']?([^"')]+)["']?\s*\)/g, type: 'css-url' },
            // JavaScript 中的 import 语句
            { regex: /import\s+.*from\s+["']([^"']+)["']/g, type: 'js-import' },
            // JavaScript 中的 require 语句
            { regex: /require\s*\(\s*["']([^"']+)["']\s*\)/g, type: 'js-require' },
            // 字符串中的路径引用
            { regex: /["']([^"']*\/static\/[^"']+)["']/g, type: 'string-path' },
            // background-image 属性
            { regex: /background-image\s*:\s*url\s*\(\s*["']?([^"')]+)["']?\s*\)/g, type: 'css-background' }
        ];
        
        lines.forEach((line, lineIndex) => {
            // 跳过注释行
            if (line.trim().startsWith('//') || line.trim().startsWith('/*') || 
                line.trim().startsWith('*') || line.trim().startsWith('<!--')) {
                return;
            }
            
            for (const pattern of patterns) {
                let match;
                while ((match = pattern.regex.exec(line)) !== null) {
                    const resourcePath = match[1];
                    
                    // 过滤掉明显不是静态资源的路径
                    if (this.isStaticResourcePath(resourcePath)) {
                        references.push({
                            path: resourcePath,
                            line: lineIndex + 1,
                            type: pattern.type
                        });
                    }
                }
                // 重置正则表达式的 lastIndex
                pattern.regex.lastIndex = 0;
            }
        });
        
        return references;
    }

    /**
     * 判断路径是否为静态资源路径
     */
    isStaticResourcePath(path) {
        // 检查是否包含静态资源扩展名
        const ext = path.split('.').pop()?.toLowerCase();
        return this.config.staticExtensions.includes(`.${ext}`);
    }

    /**
     * 标准化路径
     */
    normalizePath(resourcePath) {
        // 移除开头的 ./ 或 /
        let normalized = resourcePath.replace(/^\.?\//, '');
        
        // 处理 @/ 别名（指向项目根目录）
        normalized = normalized.replace(/^@\//, '');
        
        // 标准化路径分隔符
        normalized = normalized.replace(/\\/g, '/');
        
        return normalized;
    }

    /**
     * 查找未使用的资源
     */
    findUnusedResources() {
        console.log('🎯 识别未使用的资源...');
        
        const unusedResources = [];
        
        for (const [path, resource] of this.staticResources) {
            if (!resource.isUsed) {
                // 检查是否在白名单中
                const isWhitelisted = this.config.whitelist.some(pattern => {
                    if (pattern.endsWith('/')) {
                        return path.startsWith(pattern);
                    }
                    return path === pattern;
                });
                
                if (!isWhitelisted) {
                    // 评估风险等级
                    resource.riskLevel = this.assessRiskLevel(resource);
                    unusedResources.push(resource);
                }
            }
        }
        
        // 按大小排序
        unusedResources.sort((a, b) => b.size - a.size);
        
        console.log(`🗑️  发现 ${unusedResources.length} 个未使用的资源`);
        return unusedResources;
    }

    /**
     * 评估资源的风险等级
     */
    assessRiskLevel(resource) {
        // 高风险：系统关键文件
        if (resource.path.includes('tabbar') || 
            resource.path.includes('logo') ||
            resource.path.includes('default')) {
            return 'high';
        }
        
        // 中风险：可能存在动态引用
        if (resource.path.includes('icon') || 
            resource.path.includes('btn') ||
            resource.size > 100 * 1024) { // 大于100KB
            return 'medium';
        }
        
        // 低风险：普通资源文件
        return 'low';
    }

    /**
     * 生成分析报告
     */
    generateReport(unusedResources) {
        const totalSize = Array.from(this.staticResources.values())
            .reduce((sum, resource) => sum + resource.size, 0);
        
        const unusedSize = unusedResources
            .reduce((sum, resource) => sum + resource.size, 0);
        
        const report = {
            summary: {
                totalResources: this.staticResources.size,
                unusedResources: unusedResources.length,
                totalSize: totalSize,
                unusedSize: unusedSize,
                potentialSavings: unusedSize,
                savingsPercentage: ((unusedSize / totalSize) * 100).toFixed(2)
            },
            unusedByRisk: {
                low: unusedResources.filter(r => r.riskLevel === 'low'),
                medium: unusedResources.filter(r => r.riskLevel === 'medium'),
                high: unusedResources.filter(r => r.riskLevel === 'high')
            },
            unusedByType: this.groupByType(unusedResources),
            largestUnused: unusedResources.slice(0, 10) // 前10个最大的未使用文件
        };
        
        return report;
    }

    /**
     * 按文件类型分组
     */
    groupByType(resources) {
        const grouped = {};
        
        for (const resource of resources) {
            if (!grouped[resource.type]) {
                grouped[resource.type] = {
                    count: 0,
                    totalSize: 0,
                    files: []
                };
            }
            
            grouped[resource.type].count++;
            grouped[resource.type].totalSize += resource.size;
            grouped[resource.type].files.push(resource);
        }
        
        return grouped;
    }

    /**
     * 保存分析结果
     */
    async saveResults(report, unusedResources) {
        const outputDir = path.join(this.projectRoot, 'docs', 'tasks');
        
        // 确保输出目录存在
        if (!fs.existsSync(outputDir)) {
            fs.mkdirSync(outputDir, { recursive: true });
        }
        
        // 保存JSON格式的详细数据
        const jsonData = {
            timestamp: new Date().toISOString(),
            report: report,
            unusedResources: unusedResources,
            allResources: Array.from(this.staticResources.values()),
            references: Array.from(this.references.values())
        };
        
        fs.writeFileSync(
            path.join(outputDir, 'static-analysis-data.json'),
            JSON.stringify(jsonData, null, 2)
        );
        
        console.log('💾 分析结果已保存到 docs/tasks/static-analysis-data.json');
    }

    /**
     * 格式化文件大小
     */
    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    const projectRoot = process.argv[2] || process.cwd();
    const analyzer = new StaticResourceAnalyzer(projectRoot);
    
    analyzer.analyze()
        .then(report => {
            console.log('\n📊 分析摘要:');
            console.log(`总资源数: ${report.summary.totalResources}`);
            console.log(`未使用资源: ${report.summary.unusedResources}`);
            console.log(`可节省空间: ${analyzer.formatSize(report.summary.unusedSize)}`);
            console.log(`节省比例: ${report.summary.savingsPercentage}%`);
        })
        .catch(error => {
            console.error('分析失败:', error);
            process.exit(1);
        });
}

module.exports = StaticResourceAnalyzer;
